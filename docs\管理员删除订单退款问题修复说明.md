# 管理员删除订单退款问题修复说明

## 问题描述

在管理员删除需要退款的订单时出现错误：

```
【微信支付退款】： {
  out_trade_no: '17512453468405247',
  out_refund_no: '17512453468405247',
  reason: '用户申请退款',
  amount: { refund: 1, total: 0, currency: 'CNY' }
}

错误信息：{
  code: 'PARAM_ERROR',
  detail: [Object],
  message: '输入源"/body/amount/total"映射到数值字段"原订单金额"规则校验失败，值低于最小值 1'
}
```

## 问题分析

### 根本原因
微信支付退款接口的参数错误：
- `total`: 原订单金额为 0
- `refund`: 退款金额为 1（实际是0.01元）

这违反了微信支付的规则：
1. 原订单金额不能为0
2. 退款金额不能大于原订单金额

### 问题源头
在管理员删除订单的退款逻辑中，计算退款金额时包含了追加服务的金额：

```typescript
// 错误的计算方式
const totalRefundAmount = Number(order.totalFee) + Number(order.additionalServiceAmount || 0);
```

但微信退款接口只能退款主订单的金额，追加服务需要单独退款。

## 修复方案

### 1. 分离退款逻辑
- **追加服务退款**：使用追加服务自己的退款接口
- **主订单退款**：只退款主订单的金额（`order.totalFee`）

### 2. 处理0元订单
对于主订单金额为0的情况（如全部用优惠券抵扣），直接更新订单状态，不调用微信退款接口。

### 3. 修复后的代码逻辑

```typescript
// 1. 先处理追加服务退款
if (paidAdditionalServices.length > 0) {
  for (const additionalService of paidAdditionalServices) {
    await this.additionalServiceOrderService.refundAdditionalServiceOrder(
      additionalService.id,
      `管理员删除订单前自动退款${reason ? `，删除原因：${reason}` : ''}`,
      true // 删除时总是退回优惠券
    );
  }
}

// 2. 处理主订单退款
if (isMainOrderPaid) {
  const mainOrderRefundAmount = Number(order.totalFee);

  // 如果主订单金额为0，直接更新状态
  if (mainOrderRefundAmount === 0) {
    await order.update({ status: OrderStatus.已退款 });
    // 记录日志...
  } else {
    // 正常退款流程
    await this.auditRefund({
      sn: order.sn,
      result: true,
      reason: `管理员删除订单前自动退款`,
      money: mainOrderRefundAmount, // 只退款主订单金额
    });
  }
}
```

## 修复效果

### 修复前
- 退款金额 = 主订单金额 + 追加服务金额
- 可能导致退款金额超过原订单金额
- 0元订单也会调用微信退款接口

### 修复后
- 追加服务单独退款
- 主订单只退款自己的金额
- 0元订单直接更新状态，不调用微信接口
- 避免了参数错误

## 测试验证

### 测试场景1：有追加服务的订单
```
主订单：100元
追加服务：50元 + 30元
总计：180元

修复前：尝试退款180元（错误）
修复后：
- 先退款追加服务：50元 + 30元
- 再退款主订单：100元
```

### 测试场景2：0元主订单
```
主订单：0元（全部优惠券抵扣）
追加服务：50元

修复前：尝试退款50元到0元订单（错误）
修复后：
- 先退款追加服务：50元
- 主订单直接更新状态（不调用微信接口）
```

### 测试场景3：普通订单
```
主订单：200元
无追加服务

修复前：退款200元（正常）
修复后：退款200元（正常，无变化）
```

## 相关文件修改

### 主要修改
- `src/service/order.service.ts` - 修复退款金额计算逻辑

### 修改内容
1. 分离主订单和追加服务的退款逻辑
2. 添加0元订单的特殊处理
3. 确保退款金额不超过原订单金额

## 注意事项

1. **追加服务退款**：追加服务有自己的微信支付订单号，需要单独退款
2. **0元订单处理**：0元订单不能调用微信退款接口，需要直接更新状态
3. **金额计算**：主订单退款金额 = `order.totalFee`，不包含追加服务金额
4. **日志记录**：保持完整的操作日志，便于问题追踪

## 预防措施

1. **参数验证**：在调用微信退款前验证金额参数
2. **单元测试**：添加各种场景的测试用例
3. **日志监控**：监控退款操作的成功率和错误信息
4. **金额校验**：确保退款金额不超过原订单金额

## 总结

此次修复解决了管理员删除订单时的退款参数错误问题，通过分离主订单和追加服务的退款逻辑，确保了退款操作的正确性和稳定性。修复后的代码能够正确处理各种订单场景，包括有追加服务的订单和0元订单。
