# 管理员删除订单事务优化说明

## 优化背景

根据用户反馈，管理员删除订单功能需要进行以下优化：

1. **使用事务**：确保删除前退款的原子性，防止中间出错
2. **提前删除追加服务**：避免外键约束问题，预先处理相关退款
3. **处理微信退款异常**：防止微信已退款但数据库更新失败导致的二次退款问题

## 优化方案

### 1. 事务管理
使用数据库事务确保整个删除流程的原子性：

```typescript
// 开启事务
const transaction = await this.getModel().sequelize.transaction();

try {
  // 所有操作都在事务中执行
  // ...
  
  // 提交事务
  await transaction.commit();
} catch (error) {
  // 回滚事务
  await transaction.rollback();
  throw error;
}
```

### 2. 删除流程优化

#### 原有流程问题
1. 退款追加服务（状态变为 `REFUNDED`，记录仍存在）
2. 退款主订单
3. 删除订单 ❌ **外键约束失败**

#### 优化后流程
1. **查询所有相关数据**（在事务中）
2. **退款追加服务**（在事务外，避免微信接口超时影响事务）
3. **退款主订单**（在事务外）
4. **删除追加服务记录**（在事务中）
5. **删除订单**（在事务中）
6. **提交事务**

### 3. 微信退款异常处理

#### 问题场景
- 微信退款接口调用成功
- 数据库更新失败（网络问题、事务超时等）
- 订单状态未更新，但微信已退款
- 再次删除时会尝试二次退款

#### 解决方案
```typescript
try {
  // 退款操作
  await this.auditRefund({...});
} catch (error) {
  // 检查是否是微信退款成功但数据库更新失败的情况
  if (error.message && error.message.includes('微信退款')) {
    throw new CustomError(
      `删除订单失败：${error.message}。请检查订单状态，如微信已退款但订单状态未更新，请手动处理。`
    );
  }
  throw error;
}
```

## 核心代码实现

### 主要方法结构
```typescript
async adminDeleteOrder(id: number, operatorId: number, reason?: string) {
  const transaction = await this.getModel().sequelize.transaction();
  
  try {
    // 1. 查询订单及相关数据（在事务中）
    const order = await this.findOne({...}, { transaction });
    
    // 2. 收集追加服务数据
    const allAdditionalServices = [...];
    const paidAdditionalServices = allAdditionalServices.filter(...);
    
    // 3. 处理退款逻辑（在事务外，避免微信接口超时）
    if (isMainOrderPaid || paidAdditionalServices.length > 0) {
      await this.processRefunds(...);
    }
    
    // 4. 删除追加服务记录（在事务中）
    if (allAdditionalServices.length > 0) {
      await AdditionalServiceOrder.destroy({...}, { transaction });
    }
    
    // 5. 记录操作日志（在事务中）
    await ServiceChangeLog.create({...}, { transaction });
    
    // 6. 删除订单（在事务中）
    await order.destroy({ transaction });
    
    // 7. 提交事务
    await transaction.commit();
    
    // 8. 清理微信订阅（在事务外）
    this.clearWechatSubscriptions(order.id.toString());
    
    return true;
    
  } catch (error) {
    await transaction.rollback();
    // 错误处理...
  }
}
```

### 退款处理逻辑
```typescript
// 处理追加服务退款
if (paidAdditionalServices.length > 0) {
  for (const additionalService of paidAdditionalServices) {
    await this.additionalServiceOrderService.refundAdditionalServiceOrder(
      additionalService.id,
      `管理员删除订单前自动退款${reason ? `，删除原因：${reason}` : ''}`,
      true // 删除时总是退回优惠券
    );
  }
}

// 处理主订单退款
if (isMainOrderPaid) {
  const mainOrderRefundAmount = Number(order.totalFee);
  
  if (mainOrderRefundAmount === 0) {
    // 0元订单直接更新状态
    await order.update({ status: OrderStatus.已退款 }, { transaction });
  } else {
    // 非0元订单调用微信退款
    await this.auditRefund({
      sn: order.sn,
      result: true,
      reason: `管理员删除订单前自动退款`,
      money: mainOrderRefundAmount,
    });
  }
}
```

## 优化效果

### 1. 数据一致性
- ✅ 使用事务确保操作原子性
- ✅ 避免部分成功、部分失败的情况
- ✅ 回滚机制保证数据完整性

### 2. 外键约束问题
- ✅ 提前删除追加服务记录
- ✅ 避免外键约束失败
- ✅ 级联删除机制作为备用

### 3. 微信退款异常
- ✅ 详细的错误信息提示
- ✅ 区分微信退款成功和数据库更新失败
- ✅ 提供手动处理指导

### 4. 性能优化
- ✅ 批量删除追加服务记录
- ✅ 减少数据库查询次数
- ✅ 合理的事务范围

## 测试场景

### 1. 正常删除流程
- 有追加服务的已付款订单
- 无追加服务的已付款订单
- 未付款订单

### 2. 异常场景
- 微信退款接口超时
- 数据库连接异常
- 事务超时
- 外键约束冲突

### 3. 边界情况
- 0元主订单
- 部分追加服务已退款
- 订单状态异常

## 注意事项

### 1. 事务范围
- **包含在事务中**：数据库操作（查询、更新、删除）
- **不包含在事务中**：微信接口调用、日志记录

### 2. 错误处理
- 提供详细的错误信息
- 区分不同类型的错误
- 给出具体的处理建议

### 3. 日志记录
- 记录完整的操作流程
- 包含关键参数和状态
- 便于问题排查和审计

### 4. 兼容性
- 保持向后兼容
- 支持不同的数据库配置
- 优雅降级机制

## 总结

通过使用事务管理、优化删除流程和完善错误处理，成功解决了管理员删除订单功能的以下问题：

1. **外键约束问题**：提前删除追加服务记录
2. **数据一致性问题**：使用事务确保原子性
3. **微信退款异常**：完善错误处理和提示
4. **性能问题**：优化查询和删除逻辑

优化后的删除功能更加稳定可靠，能够正确处理各种复杂场景，确保数据的完整性和一致性。
