# 管理员删除订单功能测试场景

## 测试目的

验证管理员删除订单功能的智能退款机制，确保在删除已付款订单时能够正确处理退款流程。

## 测试场景

### 场景1：删除未付款订单
**订单状态：** 待付款  
**预期行为：** 直接删除，无需退款  
**验证点：**
- 订单被成功删除
- 记录操作日志
- 清理微信订阅信息

### 场景2：删除已付款主订单（无追加服务）
**订单状态：** 待接单/待服务/已出发/服务中/已完成  
**预期行为：** 先退款再删除  
**验证点：**
- 自动申请退款
- 自动审核通过退款
- 执行微信退款
- 订单状态变为已退款
- 成功删除订单
- 记录完整操作日志

### 场景3：删除有追加服务的订单
**订单状态：** 任意已付款状态  
**追加服务：** 部分已付款  
**预期行为：** 先退款追加服务，再退款主订单，最后删除  
**验证点：**
- 先退款所有已付款的追加服务
- 再退款主订单
- 优惠券和权益卡正确退回
- 订单成功删除
- 记录详细操作日志

### 场景4：删除已取消订单
**订单状态：** 已取消  
**预期行为：** 直接删除，无需退款  
**验证点：**
- 订单被成功删除
- 记录操作日志

### 场景5：删除已退款订单
**订单状态：** 已退款  
**预期行为：** 直接删除，无需退款  
**验证点：**
- 订单被成功删除
- 记录操作日志

### 场景6：退款失败的错误处理
**模拟条件：** 微信退款接口异常  
**预期行为：** 删除操作被阻止，返回错误信息  
**验证点：**
- 删除操作失败
- 返回详细错误信息
- 订单状态保持不变
- 记录错误日志

## 测试数据准备

### 测试订单1 - 未付款订单
```json
{
  "id": 1001,
  "sn": "TEST001",
  "status": "待付款",
  "totalFee": 100.00,
  "hasAdditionalServices": false
}
```

### 测试订单2 - 已付款主订单
```json
{
  "id": 1002,
  "sn": "TEST002", 
  "status": "待服务",
  "totalFee": 200.00,
  "hasAdditionalServices": false
}
```

### 测试订单3 - 有追加服务的订单
```json
{
  "id": 1003,
  "sn": "TEST003",
  "status": "服务中",
  "totalFee": 300.00,
  "hasAdditionalServices": true,
  "additionalServiceAmount": 150.00,
  "orderDetails": [
    {
      "id": 2001,
      "additionalServiceOrders": [
        {
          "id": 3001,
          "sn": "ADD001",
          "status": "paid",
          "totalFee": 80.00
        },
        {
          "id": 3002,
          "sn": "ADD002", 
          "status": "paid",
          "totalFee": 70.00
        }
      ]
    }
  ]
}
```

## API测试示例

### 删除未付款订单
```bash
curl -X DELETE "http://localhost:3001/admin/orders/1001/admin-delete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operatorId": 1,
    "reason": "测试删除未付款订单"
  }'
```

### 删除已付款订单
```bash
curl -X DELETE "http://localhost:3001/admin/orders/1002/admin-delete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operatorId": 1,
    "reason": "测试删除已付款订单"
  }'
```

### 删除有追加服务的订单
```bash
curl -X DELETE "http://localhost:3001/admin/orders/1003/admin-delete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operatorId": 1,
    "reason": "测试删除有追加服务的订单"
  }'
```

## 预期响应

### 成功响应
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 删除成功的订单信息
  }
}
```

### 错误响应（退款失败）
```json
{
  "errCode": 400,
  "msg": "删除订单失败：退款过程中出现错误 - 微信退款接口异常"
}
```

## 日志检查点

### 成功删除的日志示例
```
INFO: 管理员删除订单TEST002，检测到付款信息，开始退款流程
INFO: 已退款2个追加服务订单
INFO: 主订单退款完成，退款金额：450.00元
INFO: 管理员成功删除订单TEST002
```

### 错误处理的日志示例
```
ERROR: 管理员删除订单时退款失败: 微信退款接口异常
```

## 验证清单

- [ ] 未付款订单能直接删除
- [ ] 已付款订单先退款再删除
- [ ] 追加服务退款优先处理
- [ ] 优惠券和权益卡正确退回
- [ ] 微信订阅信息被清理
- [ ] 操作日志记录完整
- [ ] 错误情况正确处理
- [ ] 退款失败时阻止删除
- [ ] 返回信息准确详细

## 注意事项

1. **测试环境**：建议在测试环境进行，避免影响生产数据
2. **权限验证**：确保测试用户具有管理员权限
3. **数据备份**：测试前备份相关数据
4. **微信接口**：确保微信支付接口配置正确
5. **日志监控**：测试过程中密切关注日志输出
