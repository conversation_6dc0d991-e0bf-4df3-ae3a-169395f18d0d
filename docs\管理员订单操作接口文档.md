# 管理员订单操作接口文档

## 概述

为了给管理后台开启最大权限，新增了三个管理员专用的订单操作接口，允许管理员对任何状态的订单进行操作，绕过常规的状态限制。

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码
  "msg": "错误信息"
}
```

---

## 1. 管理员取消订单

**接口地址：** `POST /admin/orders/{orderId}/admin-cancel`  
**接口描述：** 管理员可以取消任何状态的订单（除了已取消和已退款的订单）  
**是否需要认证：** 是  
**使用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "客户要求取消"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operatorId | number | 是 | 操作员ID |
| reason | string | 否 | 取消原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**说明：**
- 管理员可以取消任何状态的订单，除了已取消或已退款的订单
- 取消时会自动处理卡券退回
- 会记录操作日志，包含操作员信息和取消原因

---

## 2. 管理员申请退款

**接口地址：** `POST /admin/orders/sn/{sn}/admin-apply-refund`  
**接口描述：** 管理员可以对任何状态的订单申请退款（除了已在退款流程中的订单）  
**是否需要认证：** 是  
**使用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sn | string | 是 | 订单编号 |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "服务质量问题"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operatorId | number | 是 | 操作员ID |
| reason | string | 否 | 退款原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**说明：**
- 管理员可以对任何状态的订单申请退款，除了已在退款流程中或已退款的订单
- 申请后订单状态会变为"退款中"
- 会记录操作日志，包含操作员信息和退款原因
- 后续仍需要通过现有的审核退款接口进行审核

---

## 3. 管理员删除订单

**接口地址：** `DELETE /admin/orders/{orderId}/admin-delete`  
**接口描述：** 管理员可以删除任何状态的订单  
**是否需要认证：** 是  
**使用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "测试订单，需要清理"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operatorId | number | 是 | 操作员ID |
| reason | string | 否 | 删除原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 删除成功的订单信息
  }
}
```

**说明：**
- 管理员可以删除任何状态的订单，无状态限制
- 删除前会记录操作日志，包含操作员信息和删除原因
- 会自动清除相关的微信订阅信息
- **注意：删除操作不可逆，请谨慎使用**

---

## 权限说明

这些接口专为管理员设计，具有最高权限：

1. **绕过状态限制**：不受常规订单状态流转限制
2. **完整日志记录**：所有操作都会记录详细的操作日志
3. **自动处理关联数据**：自动处理卡券退回、微信订阅清理等
4. **操作员追踪**：所有操作都需要提供操作员ID，便于追踪责任

## 使用建议

1. 这些接口权限很高，建议只给予高级管理员使用
2. 建议在前端增加二次确认机制，特别是删除操作
3. 重要操作建议填写详细的原因说明，便于后续审计
4. 定期检查操作日志，确保权限使用合规

## 与现有接口的区别

| 功能 | 现有接口 | 管理员接口 | 主要区别 |
|------|----------|------------|----------|
| 取消订单 | 只能取消待付款订单 | 可取消任何状态订单 | 无状态限制 |
| 申请退款 | 只能对待服务/已出发订单申请 | 可对任何状态订单申请 | 无状态限制 |
| 删除订单 | 只能删除已取消订单 | 可删除任何状态订单 | 无状态限制 |
| 操作记录 | 基础日志 | 详细操作日志 | 包含操作员和原因 |
